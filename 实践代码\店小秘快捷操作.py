"""
点心猫页面下拉框操作实践代码
功能：启动监听，智能计算标签页数量，批量操作下拉框选择300条/页显示
作者：DrissionPage学习实践
"""

from DrissionPage import Chromium
import time
import requests
import math

def get_total_products():
    """获取商品总数"""
    print("正在获取商品总数...")

    try:
        # 访问API获取商品数量信息
        api_url = "https://www.dianxiaomi.com/api/pddkjProduct/getOfflineCounts.json"
        response = requests.get(api_url)

        if response.status_code == 200:
            data = response.json()
            print(f"✓ API响应成功: {data}")

            # 从响应中获取失败商品数量
            if data.get('code') == 0 and 'data' in data:
                publish_fail = data['data'].get('publishFail', 0)
                print(f"✓ 获取到失败商品总数: {publish_fail}")
                return publish_fail
            else:
                print(f"✗ API响应格式异常: {data}")
                return 0
        else:
            print(f"✗ API请求失败，状态码: {response.status_code}")
            return 0

    except Exception as e:
        print(f"✗ 获取商品总数失败: {e}")
        return 0

def calculate_tabs_needed(total_products, items_per_page=300):
    """计算需要打开的标签页数量"""
    if total_products <= 0:
        print("⚠ 商品总数为0，将打开1个标签页")
        return 1

    # 计算需要的页数（向上取整）
    pages_needed = math.ceil(total_products / items_per_page)
    print(f"✓ 商品总数: {total_products}")
    print(f"✓ 每页显示: {items_per_page} 条")
    print(f"✓ 需要打开标签页数量: {pages_needed}")

    return pages_needed

def setup_browser_listener(browser):
    """设置浏览器监听器"""
    print("正在设置浏览器监听器...")

    try:
        # 启动网络监听
        browser.set.load_mode.eager()  # 设置为eager模式，页面DOM加载完成即可操作
        print("✓ 浏览器监听器设置成功")
        return True
    except Exception as e:
        print(f"✗ 浏览器监听器设置失败: {e}")
        return False

def operate_single_tab(tab, tab_number):
    """操作单个标签页的下拉框"""
    print(f"\n--- 开始操作第 {tab_number} 个标签页 ---")

    # 访问目标页面
    target_url = "https://www.dianxiaomi.com/web/temu/choiceTemuList/offline?dxmOfflineState=publishFail"
    print(f"正在访问目标页面...")

    try:
        tab.get(target_url)
        print("✓ 页面访问成功")

        # 等待页面加载完成
        print("正在等待页面完全加载...")
        tab.wait.load_start()  # 等待页面开始加载
        time.sleep(3)  # 额外等待3秒确保页面完全加载
        print("✓ 页面加载完成")

    except Exception as e:
        print(f"✗ 页面访问失败: {e}")
        return False
    """主函数：执行页面操作流程"""
    print("=" * 50)
    print("开始执行点心猫页面下拉框操作")
    print("=" * 50)
    
    # 创建浏览器对象，连接到指定端口9222
    print("正在连接浏览器...")
    try:
        browser = Chromium(9222)  # 直接传入端口号
        print("✓ 浏览器连接成功")
    except Exception as e:
        print(f"✗ 浏览器连接失败: {e}")
        return
    
    # 创建新标签页
    print("\n正在创建新标签页...")
    try:
        tab = browser.new_tab()
        print("✓ 新标签页创建成功")
    except Exception as e:
        print(f"✗ 新标签页创建失败: {e}")
        return
    
    # 访问目标页面
    target_url = "https://www.dianxiaomi.com/web/temu/choiceTemuList/offline?dxmOfflineState=publishFail"
    print(f"\n正在访问目标页面: {target_url}")
    
    try:
        tab.get(target_url)
        print("✓ 页面访问成功")
        
        # 等待页面加载完成
        print("正在等待页面完全加载...")
        tab.wait.load_start()  # 等待页面开始加载
        time.sleep(3)  # 额外等待3秒确保页面完全加载
        print("✓ 页面加载完成")
        
    except Exception as e:
        print(f"✗ 页面访问失败: {e}")
        return
    
    # 查找并操作下拉框
    print("\n正在查找下拉框元素...")
    
    try:
        # 方法1：通过class属性查找下拉框
        dropdown_element = tab.ele('@class=vxe-input type--text is--controls is--suffix')
        
        if dropdown_element:
            print("✓ 找到下拉框元素（方法1：通过class属性）")
        else:
            # 方法2：通过value属性查找
            print("方法1未找到，尝试方法2：通过value属性查找...")
            dropdown_element = tab.ele('@@tag()=input@@value=300条/页')
            
            if dropdown_element:
                print("✓ 找到下拉框元素（方法2：通过value属性）")
            else:
                # 方法3：通过placeholder属性查找
                print("方法2未找到，尝试方法3：通过placeholder属性查找...")
                dropdown_element = tab.ele('@@tag()=input@@placeholder=请选择')
                
                if dropdown_element:
                    print("✓ 找到下拉框元素（方法3：通过placeholder属性）")
                else:
                    print("✗ 未找到下拉框元素，尝试查找所有input元素...")
                    # 列出所有input元素供调试
                    input_elements = tab.eles('tag:input')
                    print(f"页面中共找到 {len(input_elements)} 个input元素")
                    
                    for i, input_ele in enumerate(input_elements[:5]):  # 只显示前5个
                        print(f"Input {i+1}: class='{input_ele.attr('class')}', value='{input_ele.attr('value')}', placeholder='{input_ele.attr('placeholder')}'")
                    
                    return
        
        # 点击下拉框打开选项列表
        print("\n正在点击下拉框...")
        click_result = dropdown_element.click()
        
        if click_result:
            print("✓ 下拉框点击成功")
            time.sleep(2)  # 等待下拉选项出现
            
            # 查找300条/页选项
            print("正在查找300条/页选项...")
            
            # 方法1：通过文本内容查找
            option_300 = tab.ele('@text()=300条/页')
            
            if option_300:
                print("✓ 找到300条/页选项（方法1：通过文本内容）")
            else:
                # 方法2：查找包含"300"的选项
                print("方法1未找到，尝试方法2：查找包含'300'的选项...")
                option_300 = tab.ele('@text():300')
                
                if option_300:
                    print("✓ 找到包含300的选项（方法2）")
                else:
                    print("方法2未找到，列出所有可见的选项...")
                    # 查找所有可能的选项元素
                    options = tab.eles('@@tag()=div@@text():条/页')
                    if not options:
                        options = tab.eles('@@tag()=li@@text():条/页')
                    if not options:
                        options = tab.eles('@@tag()=span@@text():条/页')
                    
                    print(f"找到 {len(options)} 个选项")
                    for i, option in enumerate(options):
                        print(f"选项 {i+1}: {option.text}")
                        if '300' in option.text:
                            option_300 = option
                            print(f"✓ 找到300条/页选项：{option.text}")
                            break
            
            # 点击300条/页选项
            if option_300:
                print("\n正在点击300条/页选项...")
                click_option_result = option_300.click()
                
                if click_option_result:
                    print("✓ 300条/页选项点击成功")
                    time.sleep(2)  # 等待页面刷新
                    
                    # 验证设置是否生效
                    print("正在验证设置是否生效...")

                    # 重新查找下拉框元素，因为页面可能已刷新
                    time.sleep(1)  # 等待页面更新
                    updated_dropdown = tab.ele('@class=vxe-input type--text is--controls is--suffix')

                    if updated_dropdown:
                        current_value = updated_dropdown.attr('value')
                        print(f"当前下拉框显示值: {current_value}")

                        # 也检查元素的文本内容
                        dropdown_text = updated_dropdown.text
                        print(f"下拉框文本内容: {dropdown_text}")

                        if ('300' in str(current_value)) or ('300' in str(dropdown_text)):
                            print("✓ 设置成功！页面已切换为300条/页显示")
                        else:
                            # 尝试通过其他方式验证，比如查找分页信息
                            print("正在通过其他方式验证设置...")

                            # 查找包含"300"的文本元素
                            elements_with_300 = tab.eles('@text():300')
                            if elements_with_300:
                                print(f"✓ 在页面中找到 {len(elements_with_300)} 个包含'300'的元素")
                                for i, ele in enumerate(elements_with_300[:3]):  # 只显示前3个
                                    print(f"  元素{i+1}: {ele.text}")
                                print("✓ 设置很可能已生效！")
                            else:
                                print("⚠ 设置可能未生效，但操作已完成，请手动检查页面")
                    else:
                        print("⚠ 无法重新定位下拉框元素，但操作已完成，请手动检查页面")
                        
                else:
                    print("✗ 300条/页选项点击失败")
            else:
                print("✗ 未找到300条/页选项")
                
        else:
            print("✗ 下拉框点击失败")
            
    except Exception as e:
        print(f"✗ 操作过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("操作完成！")
    print("注意：根据安全要求，不会自动关闭标签页和浏览器")
    print("=" * 50)

if __name__ == "__main__":
    main()
